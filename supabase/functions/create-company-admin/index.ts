import { serve } from "https://deno.land/std@0.168.0/http/server.ts"
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2'

const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
}

interface CreateAdminRequest {
  companyId: string;
  adminEmail: string;
  adminFirstName: string;
  adminLastName: string;
  adminPhone: string;
}

interface CreateAdminResponse {
  success: boolean;
  userId?: string;
  message?: string;
  error?: string;
}

const handler = async (req: Request): Promise<Response> => {
  if (req.method === 'OPTIONS') {
    return new Response(null, { headers: corsHeaders });
  }

  try {
    // Create Supabase client with service role key for admin operations
    const supabaseAdmin = createClient(
      Deno.env.get('SUPABASE_URL') ?? '',
      Deno.env.get('SUPABASE_SERVICE_ROLE_KEY') ?? ''
    );

    // Verify the request is from an authenticated super admin
    const authHeader = req.headers.get('Authorization');
    if (!authHeader) {
      console.error('No authorization header provided');
      throw new Error('No authorization header provided');
    }

    const token = authHeader.replace('Bearer ', '');
    console.log('Verifying user with token...');
    const { data: { user }, error: authError } = await supabaseAdmin.auth.getUser(token);

    if (authError || !user) {
      console.error('Auth error:', authError);
      throw new Error(`Authentication failed: ${authError?.message || 'User not found'}`);
    }

    console.log('User authenticated:', user.id);

    // Check if user is super admin
    console.log('Checking super admin role for user:', user.id);
    const { data: userRole, error: roleError } = await supabaseAdmin
      .from('user_roles')
      .select('role')
      .eq('user_id', user.id)
      .eq('role', 'super_admin')
      .single();

    console.log('Role query result:', { userRole, roleError });

    if (roleError || !userRole) {
      console.error('Role verification failed:', roleError);
      throw new Error(`Unauthorized: Only super admins can create company admins. Role error: ${roleError?.message || 'No super admin role found'}`);
    }

    const { companyId, adminEmail, adminFirstName, adminLastName, adminPhone }: CreateAdminRequest = await req.json();

    // Validate required fields
    if (!companyId || !adminEmail || !adminFirstName || !adminLastName) {
      throw new Error('Missing required fields: companyId, adminEmail, adminFirstName, adminLastName');
    }

    // Verify company exists
    const { data: company, error: companyError } = await supabaseAdmin
      .from('companies')
      .select('id, name')
      .eq('id', companyId)
      .single();

    if (companyError || !company) {
      throw new Error('Company not found');
    }



    // Create admin user directly with a default password
    const defaultPassword = 'Admin123!'; // Default password - admin should change it

    const { data: newUser, error: createError } = await supabaseAdmin.auth.admin.createUser({
      email: adminEmail,
      password: defaultPassword,
      user_metadata: {
        full_name: `${adminFirstName} ${adminLastName}`,
        role: 'company_admin',
        company_id: companyId,
        company_name: company.name
      },
      email_confirm: true // Auto-confirm email since super admin is creating
    });

    if (createError) {
      if (createError.message.includes('already registered')) {
        throw new Error('A user with this email already exists');
      }
      throw createError;
    }

    if (!newUser.user) {
      throw new Error('Failed to create admin user');
    }

    // Create user role record
    const { error: roleInsertError } = await supabaseAdmin
      .from('user_roles')
      .insert({
        user_id: newUser.user.id,
        role: 'company_admin',
        company_id: companyId,
        created_at: new Date().toISOString()
      });

    if (roleInsertError) {
      console.error('Error creating user role:', roleInsertError);
      // Try to clean up the created user
      await supabaseAdmin.auth.admin.deleteUser(newUser.user.id);
      throw new Error('Failed to assign admin role');
    }

    // Log the successful creation
    console.log(`Successfully created admin user for company ${company.name}:`, {
      userId: newUser.user.id,
      email: adminEmail,
      companyId: companyId
    });

    const response: CreateAdminResponse = {
      success: true,
      userId: newUser.user.id,
      message: `Admin user created successfully for ${company.name}. Email: ${adminEmail}, Password: ${defaultPassword} (please change on first login)`
    };

    return new Response(
      JSON.stringify(response),
      { 
        headers: { 
          ...corsHeaders, 
          'Content-Type': 'application/json' 
        } 
      }
    );

  } catch (error) {
    console.error('Error in create-company-admin function:', error);
    
    const response: CreateAdminResponse = {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error occurred'
    };

    return new Response(
      JSON.stringify(response),
      { 
        status: 400,
        headers: { 
          ...corsHeaders, 
          'Content-Type': 'application/json' 
        } 
      }
    );
  }
};

serve(handler);

import { serve } from "https://deno.land/std@0.168.0/http/server.ts";
import { createClient } from "https://esm.sh/@supabase/supabase-js@2";

const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
};

serve(async (req) => {
  // Handle CORS preflight requests
  if (req.method === 'OPTIONS') {
    return new Response(null, { headers: corsHeaders });
  }

  try {
    // Parse form data from Twilio webhook
    const formData = await req.formData();
    const callSid = formData.get('CallSid')?.toString();
    const callStatus = formData.get('CallStatus')?.toString();
    const callDuration = formData.get('CallDuration')?.toString();
    const from = formData.get('From')?.toString();
    const to = formData.get('To')?.toString();

    console.log('Twilio webhook received:', {
      callSid,
      callStatus,
      callDuration,
      from,
      to
    });

    if (!callSid || !callStatus) {
      throw new Error('Missing required webhook parameters');
    }

    // Create Supabase client
    const supabaseUrl = Deno.env.get('SUPABASE_URL')!;
    const supabaseServiceKey = Deno.env.get('SUPABASE_SERVICE_ROLE_KEY')!;
    const supabase = createClient(supabaseUrl, supabaseServiceKey);

    // Update call log with status and duration
    const updateData: any = {
      status: callStatus,
    };

    if (callDuration) {
      updateData.duration = parseInt(callDuration);
    }

    if (callStatus === 'completed' || callStatus === 'failed' || callStatus === 'canceled') {
      updateData.ended_at = new Date().toISOString();
    }

    const { data: callLog, error: updateError } = await supabase
      .from('call_logs')
      .update(updateData)
      .eq('twilio_call_sid', callSid)
      .select()
      .single();

    if (updateError) {
      console.error('Failed to update call log:', updateError);
      throw new Error('Failed to update call log');
    }

    // Add activity log for status change
    if (callLog) {
      await supabase
        .from('lead_activities')
        .insert({
          user_id: callLog.user_id,
          lead_id: callLog.lead_id,
          activity_type: 'call',
          description: `Call ${callStatus}${callDuration ? ` (${callDuration}s)` : ''}`,
          metadata: { 
            call_sid: callSid, 
            status: callStatus,
            duration: callDuration ? parseInt(callDuration) : null
          },
          company_id: callLog.company_id
        });
    }

    return new Response('OK', {
      headers: { ...corsHeaders, 'Content-Type': 'text/plain' },
    });

  } catch (error) {
    console.error('Error in twilio-webhook function:', error);
    return new Response('Internal Server Error', {
      status: 500,
      headers: { ...corsHeaders, 'Content-Type': 'text/plain' },
    });
  }
});
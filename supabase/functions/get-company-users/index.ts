import { serve } from "https://deno.land/std@0.168.0/http/server.ts"
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2.45.0'

const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
}

interface GetCompanyUsersRequest {
  companyId: string;
}

const handler = async (req: Request): Promise<Response> => {
  if (req.method === 'OPTIONS') {
    return new Response(null, { headers: corsHeaders });
  }

  try {
    const supabaseAdmin = createClient(
      Deno.env.get('SUPABASE_URL') ?? '',
      Deno.env.get('SUPABASE_SERVICE_ROLE_KEY') ?? ''
    );

    const body: GetCompanyUsersRequest = await req.json();
    const { companyId } = body;

    if (!companyId) {
      return new Response(
        JSON.stringify({ success: false, error: 'companyId is required' }),
        { status: 400, headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
      );
    }

    console.log('Fetching users for company:', companyId);

    // Get user roles for the company
    const { data: userRoles, error: rolesError } = await supabaseAdmin
      .from('user_roles')
      .select(`
        user_id,
        role,
        created_at,
        companies!inner(
          id,
          name
        )
      `)
      .eq('company_id', companyId)
      .neq('role', 'super_admin'); // Exclude super admins from company user lists

    if (rolesError) {
      console.error('Error fetching user roles:', rolesError);
      throw new Error('Failed to fetch user roles: ' + rolesError.message);
    }

    if (!userRoles || userRoles.length === 0) {
      return new Response(
        JSON.stringify({
          success: true,
          users: []
        }),
        { status: 200, headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
      );
    }

    // Get auth user data for each user
    const userIds = userRoles.map(role => role.user_id);
    const { data: authUsers, error: authError } = await supabaseAdmin.auth.admin.listUsers();
    
    if (authError) {
      console.error('Error fetching auth users:', authError);
      throw new Error('Failed to fetch auth users: ' + authError.message);
    }

    // Create a map of auth users by ID for quick lookup
    const authUserMap = new Map();
    authUsers.users.forEach(user => {
      authUserMap.set(user.id, user);
    });

    // Combine role data with auth data
    const enrichedUsers = userRoles.map(roleData => {
      const authUser = authUserMap.get(roleData.user_id);

      return {
        id: roleData.user_id,
        email: authUser?.email || 'Unknown',
        full_name: authUser?.user_metadata?.full_name || authUser?.user_metadata?.name || 'Unknown User',
        role: roleData.role,
        created_at: roleData.created_at,
        last_sign_in_at: authUser?.last_sign_in_at,
        email_confirmed_at: authUser?.email_confirmed_at,
        is_active: !authUser?.banned_until,
        status: authUser?.email_confirmed_at ? 'active' : 'pending',
        company_id: companyId,
        company_name: roleData.companies?.name || 'Unknown Company'
      };
    });

    console.log(`Successfully fetched ${enrichedUsers.length} users for company ${companyId}`);

    return new Response(
      JSON.stringify({
        success: true,
        users: enrichedUsers
      }),
      { status: 200, headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
    );

  } catch (error) {
    console.error('Error in get-company-users:', error);
    return new Response(
      JSON.stringify({ 
        success: false, 
        error: error.message || 'Internal server error' 
      }),
      { status: 500, headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
    );
  }
};

serve(handler);

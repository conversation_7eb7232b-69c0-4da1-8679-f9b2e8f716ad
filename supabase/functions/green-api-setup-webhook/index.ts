import { createClient } from 'https://esm.sh/@supabase/supabase-js@2';
import { serve } from 'https://deno.land/std@0.190.0/http/server.ts';

const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
};

interface SetupWebhookRequest {
  companyId: string;
}

const handler = async (req: Request): Promise<Response> => {
  if (req.method === 'OPTIONS') {
    return new Response(null, { headers: corsHeaders });
  }

  try {
    const supabaseClient = createClient(
      Deno.env.get('SUPABASE_URL') ?? '',
      Deno.env.get('SUPABASE_SERVICE_ROLE_KEY') ?? ''
    );

    const authHeader = req.headers.get('Authorization')!;
    const token = authHeader.replace('Bearer ', '');
    const { data: { user } } = await supabaseClient.auth.getUser(token);

    if (!user) {
      throw new Error('Unauthorized');
    }

    const { companyId }: SetupWebhookRequest = await req.json();

    // Get company's GreenAPI credentials
    const { data: company } = await supabaseClient
      .from('companies')
      .select('green_api_instance_id, green_api_token')
      .eq('id', companyId)
      .single();

    if (!company?.green_api_instance_id || !company?.green_api_token) {
      throw new Error('GreenAPI credentials not configured for company');
    }

    const webhookUrl = `${Deno.env.get('SUPABASE_URL')}/functions/v1/green-api-webhook?instanceId=${company.green_api_instance_id}`;

    // Set webhook URL in GreenAPI - Extract first 4 digits for subdomain
    const instanceSubdomain = company.green_api_instance_id.substring(0, 4);
    const setWebhookUrl = `https://${instanceSubdomain}.api.greenapi.com/waInstance${company.green_api_instance_id}/setSettings/${company.green_api_token}`;
    
    const webhookResponse = await fetch(setWebhookUrl, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        webhookUrl: webhookUrl,
        webhookUrlToken: '',
        delaySendMessagesMilliseconds: 1000,
        markIncomingMessagesReaded: 'no',
        proxyInstance: '',
        outgoingWebhook: 'yes',
        incomingWebhook: 'yes',
        deviceWebhook: 'no',
        statusInstanceWebhook: 'no',
        stateWebhook: 'no',
        enableMessagesHistory: 'no',
        keepOnlineStatus: 'no'
      }),
    });

    const webhookResult = await webhookResponse.json();
    console.log('GreenAPI webhook setup response:', webhookResult);

    if (!webhookResponse.ok) {
      throw new Error(`GreenAPI webhook setup error: ${webhookResult.error || 'Unknown error'}`);
    }

    return new Response(
      JSON.stringify({ 
        success: true, 
        webhookUrl: webhookUrl,
        message: 'Webhook configured successfully' 
      }),
      {
        status: 200,
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
      }
    );
  } catch (error) {
    console.error('Error in green-api-setup-webhook:', error);
    return new Response(
      JSON.stringify({ error: error.message }),
      {
        status: 500,
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
      }
    );
  }
};

serve(handler);
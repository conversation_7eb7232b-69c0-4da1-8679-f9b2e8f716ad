import { serve } from "https://deno.land/std@0.168.0/http/server.ts";
import { createClient } from "https://esm.sh/@supabase/supabase-js@2";

const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
};

// Utility function to format Israeli phone numbers to E.164 format
function formatPhoneToE164(phoneNumber: string): string {
  // Remove any spaces, dashes, or other formatting
  const cleanNumber = phoneNumber.replace(/\s|-|\(|\)/g, '');
  
  // If already starts with +972, return as is
  if (cleanNumber.startsWith('+972')) {
    return cleanNumber;
  }
  
  // If starts with 972 (without +), add the +
  if (cleanNumber.startsWith('972')) {
    return '+' + cleanNumber;
  }
  
  // If starts with 0 (Israeli local format), convert to international
  if (cleanNumber.startsWith('0')) {
    return '+972' + cleanNumber.substring(1);
  }
  
  // If it's just the number without country code or leading 0, assume Israeli
  if (cleanNumber.length === 9 || cleanNumber.length === 10) {
    return '+972' + cleanNumber;
  }
  
  // Return as is if we can't determine the format
  return cleanNumber;
}

serve(async (req) => {
  // Handle CORS preflight requests
  if (req.method === 'OPTIONS') {
    return new Response(null, { headers: corsHeaders });
  }

  try {
    const { leadId, phoneNumber } = await req.json();

    // Create Supabase client
    const supabaseUrl = Deno.env.get('SUPABASE_URL')!;
    const supabaseServiceKey = Deno.env.get('SUPABASE_SERVICE_ROLE_KEY')!;
    const supabase = createClient(supabaseUrl, supabaseServiceKey);

    // Get user from auth header
    const authHeader = req.headers.get('Authorization');
    if (!authHeader) {
      throw new Error('Missing authorization header');
    }

    const token = authHeader.replace('Bearer ', '');
    const { data: { user }, error: authError } = await supabase.auth.getUser(token);
    
    if (authError || !user) {
      throw new Error('Invalid authorization');
    }
    
    // Get user's company and Twilio credentials
    const { data: userRole, error: roleError } = await supabase
      .from('user_roles')
      .select('company_id')
      .eq('user_id', user.id)
      .single();

    if (roleError || !userRole) {
      throw new Error('User not associated with any company');
    }

    const { data: company, error: companyError } = await supabase
      .from('companies')
      .select('twilio_account_sid, twilio_auth_token, twilio_phone_number')
      .eq('id', userRole.company_id)
      .single();

    if (companyError || !company) {
      throw new Error('Company not found or Twilio credentials not configured');
    }

    const accountSid = company.twilio_account_sid;
    const authToken = company.twilio_auth_token;
    const twilioPhoneNumber = company.twilio_phone_number;

    if (!accountSid || !authToken || !twilioPhoneNumber) {
      throw new Error('Missing Twilio credentials');
    }

    // Format phone number to E.164 international format
    const formattedPhoneNumber = formatPhoneToE164(phoneNumber);
    console.log(`Formatted phone number: ${phoneNumber} -> ${formattedPhoneNumber}`);

    // Create TwiML for the call - this will connect the caller to the lead
    const twiml = `<?xml version="1.0" encoding="UTF-8"?>
    <Response>
      <Dial>
        <Number>${formattedPhoneNumber}</Number>
      </Dial>
    </Response>`;

    // Make call using Twilio API
    const twilioUrl = `https://api.twilio.com/2010-04-01/Accounts/${accountSid}/Calls.json`;
    const callData = new URLSearchParams({
      'From': twilioPhoneNumber,
      'To': formattedPhoneNumber,
      'Twiml': twiml,
      'StatusCallback': `${supabaseUrl}/functions/v1/twilio-webhook`,
      'StatusCallbackMethod': 'POST'
    });

    const twilioResponse = await fetch(twilioUrl, {
      method: 'POST',
      headers: {
        'Authorization': 'Basic ' + btoa(`${accountSid}:${authToken}`),
        'Content-Type': 'application/x-www-form-urlencoded',
      },
      body: callData,
    });

    if (!twilioResponse.ok) {
      const errorText = await twilioResponse.text();
      console.error('Twilio API Error:', errorText);
      throw new Error(`Twilio API Error: ${twilioResponse.status}`);
    }

    const callResult = await twilioResponse.json();
    console.log('Twilio call initiated:', callResult);

    // Create call log in database
    const { error: dbError } = await supabase
      .from('call_logs')
      .insert({
        user_id: user.id,
        lead_id: leadId,
        twilio_call_sid: callResult.sid,
        phone_number: phoneNumber,
        direction: 'outbound',
        status: 'initiated',
        company_id: userRole.company_id
      });

    if (dbError) {
      console.error('Failed to create call log:', dbError);
      throw new Error('Failed to log call');
    }

    // Log activity
    await supabase
      .from('lead_activities')
      .insert({
        user_id: user.id,
        lead_id: leadId,
        activity_type: 'call',
        description: `Call initiated to ${phoneNumber}`,
        metadata: { call_sid: callResult.sid, status: 'initiated' },
        company_id: userRole.company_id
      });

    return new Response(JSON.stringify({ 
      success: true, 
      callSid: callResult.sid,
      status: callResult.status
    }), {
      headers: { ...corsHeaders, 'Content-Type': 'application/json' },
    });

  } catch (error) {
    console.error('Error in twilio-call function:', error);
    return new Response(JSON.stringify({ 
      error: error.message 
    }), {
      status: 500,
      headers: { ...corsHeaders, 'Content-Type': 'application/json' },
    });
  }
});
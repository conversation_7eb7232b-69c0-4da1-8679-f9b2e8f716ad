import { serve } from "https://deno.land/std@0.168.0/http/server.ts";
import { createClient } from "https://esm.sh/@supabase/supabase-js@2";

const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
};

serve(async (req) => {
  // Handle CORS preflight requests
  if (req.method === 'OPTIONS') {
    return new Response(null, { headers: corsHeaders });
  }

  try {
    const { action, callSid } = await req.json();
    
    // Get authorization header
    const authHeader = req.headers.get('Authorization');
    if (!authHeader) {
      throw new Error('Missing authorization header');
    }

    // Create Supabase client
    const supabaseUrl = Deno.env.get('SUPABASE_URL')!;
    const supabaseServiceKey = Deno.env.get('SUPABASE_SERVICE_ROLE_KEY')!;
    const supabase = createClient(supabaseUrl, supabaseServiceKey, {
      global: {
        headers: { Authorization: authHeader },
      },
    });

    // Get user from auth
    const { data: { user }, error: userError } = await supabase.auth.getUser();
    if (userError || !user) {
      throw new Error('Unauthorized');
    }

    // Get call log to find company
    const { data: callLog, error: callLogError } = await supabase
      .from('call_logs')
      .select('company_id')
      .eq('twilio_call_sid', callSid)
      .single();

    if (callLogError || !callLog) {
      throw new Error('Call not found');
    }

    // Get company Twilio credentials
    const { data: company, error: companyError } = await supabase
      .from('companies')
      .select('twilio_account_sid, twilio_auth_token')
      .eq('id', callLog.company_id)
      .single();

    if (companyError || !company) {
      throw new Error('Company Twilio credentials not found');
    }

    const accountSid = company.twilio_account_sid;
    const authToken = company.twilio_auth_token;

    if (!accountSid || !authToken) {
      throw new Error('Missing Twilio credentials');
    }

    if (!callSid || !action) {
      throw new Error('Missing required parameters: callSid and action');
    }

    let twilioUrl = '';
    let method = 'POST';
    let body: URLSearchParams | null = null;

    switch (action) {
      case 'hangup':
        // Update call to completed status
        twilioUrl = `https://api.twilio.com/2010-04-01/Accounts/${accountSid}/Calls/${callSid}.json`;
        method = 'POST';
        body = new URLSearchParams({
          'Status': 'completed'
        });
        break;
        
      case 'mute':
        // Modify call to mute participant
        twilioUrl = `https://api.twilio.com/2010-04-01/Accounts/${accountSid}/Calls/${callSid}.json`;
        method = 'POST';
        body = new URLSearchParams({
          'Twiml': `<?xml version="1.0" encoding="UTF-8"?><Response><Say>Call muted</Say><Pause length="3600"/></Response>`
        });
        break;
        
      case 'unmute':
        // Resume normal call flow
        twilioUrl = `https://api.twilio.com/2010-04-01/Accounts/${accountSid}/Calls/${callSid}.json`;
        method = 'POST';
        body = new URLSearchParams({
          'Twiml': `<?xml version="1.0" encoding="UTF-8"?><Response><Say>Call resumed</Say><Dial><Number>${req.headers.get('x-phone-number') || ''}</Number></Dial></Response>`
        });
        break;
        
      default:
        throw new Error(`Unsupported action: ${action}`);
    }

    // Make request to Twilio API
    const twilioResponse = await fetch(twilioUrl, {
      method,
      headers: {
        'Authorization': 'Basic ' + btoa(`${accountSid}:${authToken}`),
        'Content-Type': 'application/x-www-form-urlencoded',
      },
      body: body,
    });

    if (!twilioResponse.ok) {
      const errorText = await twilioResponse.text();
      console.error('Twilio API Error:', errorText);
      throw new Error(`Twilio API Error: ${twilioResponse.status}`);
    }

    const result = await twilioResponse.json();
    console.log(`Call ${action} successful:`, result);

    return new Response(JSON.stringify({ 
      success: true, 
      action,
      callSid,
      result
    }), {
      headers: { ...corsHeaders, 'Content-Type': 'application/json' },
    });

  } catch (error) {
    console.error('Error in call-control function:', error);
    return new Response(JSON.stringify({ 
      error: error.message 
    }), {
      status: 500,
      headers: { ...corsHeaders, 'Content-Type': 'application/json' },
    });
  }
});
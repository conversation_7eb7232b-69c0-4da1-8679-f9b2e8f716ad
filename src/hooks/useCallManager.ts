import { useState, useCallback } from 'react';
import { supabase } from '@/integrations/supabase/client';
import { toast } from 'sonner';

export interface ActiveCall {
  callSid: string;
  leadId: string;
  leadName: string;
  phoneNumber: string;
  startTime: Date;
  status: 'connecting' | 'connected' | 'muted' | 'ended';
}

export const useCallManager = () => {
  const [activeCall, setActiveCall] = useState<ActiveCall | null>(null);
  const [isLoading, setIsLoading] = useState(false);

  const initiateCall = useCallback(async (leadId: string, leadName: string, phoneNumber: string) => {
    setIsLoading(true);
    try {
      const { data, error } = await supabase.functions.invoke('twilio-call', {
        body: {
          leadId,
          phoneNumber: phoneNumber.replace(/\D/g, '') // Remove non-digits
        }
      });

      if (error) {
        console.error('Supabase function error:', error);
        
        // Check for specific error types
        if (error.message?.includes('Missing Twilio credentials')) {
          toast.error('Twi<PERSON> לא מוגדר - יש לפנות למנהל המערכת להגדרת הרכיבים');
          return;
        }
        
        if (error.message?.includes('User not associated with any company')) {
          toast.error('משתמש לא משויך לחברה');
          return;
        }
        
        throw error;
      }

      if (data?.success) {
        setActiveCall({
          callSid: data.callSid,
          leadId,
          leadName,
          phoneNumber,
          startTime: new Date(),
          status: 'connecting'
        });
        toast.success(`מתחיל שיחה עם ${leadName}`);
      } else {
        const errorMsg = data?.error || 'Failed to initiate call';
        throw new Error(errorMsg);
      }
    } catch (error: any) {
      console.error('Error initiating call:', error);
      
      // Show specific error message if available
      if (error.message && !error.message.includes('Failed to initiate call')) {
        toast.error(`שגיאה: ${error.message}`);
      } else {
        toast.error('שגיאה בביצוע השיחה - נסה שוב או פנה למנהל המערכת');
      }
    } finally {
      setIsLoading(false);
    }
  }, []);

  const hangupCall = useCallback(async () => {
    if (!activeCall) return;

    try {
      const { error } = await supabase.functions.invoke('call-control', {
        body: {
          action: 'hangup',
          callSid: activeCall.callSid
        }
      });

      if (error) throw error;

      setActiveCall(null);
      toast.success('השיחה הסתיימה');
    } catch (error) {
      console.error('Error hanging up call:', error);
      toast.error('שגיאה בסיום השיחה');
    }
  }, [activeCall]);

  const muteCall = useCallback(async () => {
    if (!activeCall) return;

    try {
      const { error } = await supabase.functions.invoke('call-control', {
        body: {
          action: 'mute',
          callSid: activeCall.callSid
        }
      });

      if (error) throw error;

      setActiveCall(prev => prev ? { ...prev, status: 'muted' } : null);
      toast.success('המיקרופון מושתק');
    } catch (error) {
      console.error('Error muting call:', error);
      toast.error('שגיאה בהשתקת המיקרופון');
    }
  }, [activeCall]);

  const unmuteCall = useCallback(async () => {
    if (!activeCall) return;

    try {
      const { error } = await supabase.functions.invoke('call-control', {
        body: {
          action: 'unmute',
          callSid: activeCall.callSid
        }
      });

      if (error) throw error;

      setActiveCall(prev => prev ? { ...prev, status: 'connected' } : null);
      toast.success('המיקרופון פעיל');
    } catch (error) {
      console.error('Error unmuting call:', error);
      toast.error('שגיאה בהפעלת המיקרופון');
    }
  }, [activeCall]);

  return {
    activeCall,
    isLoading,
    initiateCall,
    hangupCall,
    muteCall,
    unmuteCall
  };
};
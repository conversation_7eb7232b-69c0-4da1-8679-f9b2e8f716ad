import { useState, useEffect, useCallback, useRef } from 'react';
import { supabase } from '@/integrations/supabase/client';
import { toast } from 'sonner';
import { useCompany } from '@/contexts/CompanyContext';

export interface WhatsAppConversation {
  id: string;
  company_id: string;
  lead_id: string | null;
  phone_number: string;
  green_api_chat_id: string | null;
  contact_name: string | null;
  last_message: string | null;
  last_message_timestamp: string | null;
  unread_count: number;
  created_at: string;
  updated_at: string;
}

export interface WhatsAppMessage {
  id: string;
  conversation_id: string;
  green_api_message_id: string | null;
  content: string;
  message_type: string;
  sender_type: 'outgoing' | 'incoming';
  status: string;
  media_url: string | null;
  created_at: string;
}

export const useWhatsApp = () => {
  const [conversations, setConversations] = useState<WhatsAppConversation[]>([]);
  const [messages, setMessages] = useState<WhatsAppMessage[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [isSending, setIsSending] = useState(false);
  const { currentCompany } = useCompany();
  const abortControllerRef = useRef<AbortController | null>(null);
  const markAsReadTimeoutRef = useRef<{ [key: string]: NodeJS.Timeout }>({});

  const fetchConversations = useCallback(async () => {
    if (!currentCompany?.id) {
      setConversations([]);
      setIsLoading(false);
      return;
    }

    if (abortControllerRef.current) {
      abortControllerRef.current.abort();
    }

    abortControllerRef.current = new AbortController();

    try {
      const { data, error } = await supabase
        .from('whatsapp_conversations')
        .select('*')
        .eq('company_id', currentCompany.id)
        .order('last_message_timestamp', { ascending: false, nullsFirst: false })
        .abortSignal(abortControllerRef.current.signal);

      if (error) {
        console.error('Error fetching conversations:', error);
        toast.error('Failed to fetch conversations');
        return;
      }

      setConversations(data || []);
    } catch (error: any) {
      if (error.name !== 'AbortError') {
        console.error('Error in fetchConversations:', error);
        toast.error('Failed to fetch conversations');
      }
    } finally {
      setIsLoading(false);
    }
  }, [currentCompany?.id]);

  const fetchMessages = useCallback(async (conversationId: string) => {
    try {
      const { data, error } = await supabase
        .from('whatsapp_messages')
        .select('*')
        .eq('conversation_id', conversationId)
        .order('created_at', { ascending: true });

      if (error) {
        console.error('Error fetching messages:', error);
        toast.error('Failed to fetch messages');
        return;
      }

      setMessages((data || []) as WhatsAppMessage[]);
    } catch (error) {
      console.error('Error in fetchMessages:', error);
      toast.error('Failed to fetch messages');
    }
  }, []);

  const sendMessage = async (phoneNumber: string, message: string, leadId?: string) => {
    try {
      setIsSending(true);
      const { data, error } = await supabase.functions.invoke('green-api-send-message', {
        body: {
          phoneNumber,
          message,
          leadId
        }
      });

      if (error) {
        console.error('Error sending message:', error);
        toast.error('Failed to send message');
        throw error;
      }

      toast.success('Message sent successfully');
      await fetchConversations();
      
      if (data?.conversationId) {
        await fetchMessages(data.conversationId);
      }

      return data;
    } catch (error) {
      console.error('Error in sendMessage:', error);
      throw error;
    } finally {
      setIsSending(false);
    }
  };

  const markAsRead = useCallback(async (conversationId: string) => {
    // Clear any existing timeout for this conversation
    if (markAsReadTimeoutRef.current[conversationId]) {
      clearTimeout(markAsReadTimeoutRef.current[conversationId]);
    }
    
    // Debounce the markAsRead call
    markAsReadTimeoutRef.current[conversationId] = setTimeout(async () => {
      try {
        const { error } = await supabase
          .from('whatsapp_conversations')
          .update({ unread_count: 0 })
          .eq('id', conversationId);

        if (error) {
          console.error('Error marking as read:', error);
          return;
        }

        // Update local state
        setConversations(prev => 
          prev.map(conv => 
            conv.id === conversationId 
              ? { ...conv, unread_count: 0 }
              : conv
          )
        );
      } catch (error) {
        console.error('Error in markAsRead:', error);
      }
      
      delete markAsReadTimeoutRef.current[conversationId];
    }, 500); // 500ms debounce
  }, []);

  const findOrCreateConversation = async (phoneNumber: string, leadId?: string) => {
    try {
      // First try to find existing conversation
      const { data: existing } = await supabase
        .from('whatsapp_conversations')
        .select('id')
        .eq('phone_number', phoneNumber)
        .single();

      if (existing) {
        return existing.id;
      }

      // If no conversation exists, send a message will create one
      return null;
    } catch (error) {
      console.error('Error in findOrCreateConversation:', error);
      return null;
    }
  };

  useEffect(() => {
    fetchConversations();

    // Set up real-time subscriptions
    const conversationChannel = supabase
      .channel('whatsapp_conversations_changes')
      .on(
        'postgres_changes',
        {
          event: '*',
          schema: 'public',
          table: 'whatsapp_conversations'
        },
        () => {
          fetchConversations();
        }
      )
      .subscribe();

    const messageChannel = supabase
      .channel('whatsapp_messages_changes')
      .on(
        'postgres_changes',
        {
          event: '*',
          schema: 'public',
          table: 'whatsapp_messages'
        },
        (payload) => {
          // Refresh messages if we're viewing this conversation
          if (payload.new) {
            const conversationId = (payload.new as WhatsAppMessage).conversation_id;
            // Only refresh if we have messages loaded for this conversation
            setMessages(currentMessages => {
              if (currentMessages.length > 0 && currentMessages[0]?.conversation_id === conversationId) {
                fetchMessages(conversationId);
              }
              return currentMessages;
            });
          }
        }
      )
      .subscribe();

    return () => {
      // Cleanup abort controller
      if (abortControllerRef.current) {
        abortControllerRef.current.abort();
      }
      
      // Clear all pending timeouts
      Object.values(markAsReadTimeoutRef.current).forEach(timeout => {
        clearTimeout(timeout);
      });
      markAsReadTimeoutRef.current = {};
      
      // Remove channels
      supabase.removeChannel(conversationChannel);
      supabase.removeChannel(messageChannel);
    };
  }, [fetchConversations, fetchMessages]);

  return {
    conversations,
    messages,
    isLoading,
    isSending,
    fetchConversations,
    fetchMessages,
    sendMessage,
    markAsRead,
    findOrCreateConversation
  };
};
import React, { useState, useEffect } from 'react';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import { <PERSON><PERSON> } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Badge } from '@/components/ui/badge';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { 
  Building2, 
  Settings, 
  MessageSquare, 
  Phone, 
  Save, 
  Eye, 
  EyeOff,
  Copy,
  CheckCircle,
  AlertCircle
} from 'lucide-react';
import { useCompany } from '@/contexts/CompanyContext';
import { useAuth } from '@/hooks/useAuth';
import { supabase } from '@/integrations/supabase/client';
import { toast } from 'sonner';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';

const companyInfoSchema = z.object({
  name: z.string().min(2, 'Company name must be at least 2 characters'),
  email: z.string().email('Invalid email address'),
  phone: z.string().min(10, 'Phone number must be at least 10 characters'),
  address: z.string().min(5, 'Address must be at least 5 characters'),
});

const apiTokensSchema = z.object({
  green_api_instance_id: z.string().optional(),
  green_api_token: z.string().optional(),
  whatsapp_number: z.string().optional(),
  twilio_account_sid: z.string().optional(),
  twilio_auth_token: z.string().optional(),
  twilio_phone_number: z.string().optional(),
});

type CompanyInfoData = z.infer<typeof companyInfoSchema>;
type ApiTokensData = z.infer<typeof apiTokensSchema>;

const CompanySettingsPage = () => {
  const { currentCompany, refreshUserCompany, refreshCompanies } = useCompany();
  const { isCompanyAdmin, isSuperAdmin } = useAuth();
  const [isLoading, setIsLoading] = useState(false);
  const [showTokens, setShowTokens] = useState({
    green_api_token: false,
    twilio_auth_token: false,
  });

  // Company Info Form
  const companyForm = useForm<CompanyInfoData>({
    resolver: zodResolver(companyInfoSchema),
    defaultValues: {
      name: '',
      email: '',
      phone: '',
      address: '',
    }
  });

  // API Tokens Form
  const tokensForm = useForm<ApiTokensData>({
    resolver: zodResolver(apiTokensSchema),
    defaultValues: {
      green_api_instance_id: '',
      green_api_token: '',
      whatsapp_number: '',
      twilio_account_sid: '',
      twilio_auth_token: '',
      twilio_phone_number: '',
    }
  });

  // Load company data when component mounts or company changes
  useEffect(() => {
    if (currentCompany) {
      // Set company info form values
      companyForm.reset({
        name: currentCompany.name || '',
        email: currentCompany.email || '',
        phone: currentCompany.phone || '',
        address: currentCompany.address || '',
      });

      // Set API tokens form values
      const apiTokens = currentCompany.api_tokens || {};
      tokensForm.reset({
        green_api_instance_id: apiTokens.green_api_instance_id || '',
        green_api_token: apiTokens.green_api_token || '',
        whatsapp_number: apiTokens.whatsapp_number || '',
        twilio_account_sid: apiTokens.twilio_account_sid || '',
        twilio_auth_token: apiTokens.twilio_auth_token || '',
        twilio_phone_number: apiTokens.twilio_phone_number || '',
      });
    }
  }, [currentCompany, companyForm, tokensForm]);

  // Check permissions
  if (!isCompanyAdmin && !isSuperAdmin) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <Card className="w-96">
          <CardHeader>
            <CardTitle className="text-center text-red-600">Access Denied</CardTitle>
          </CardHeader>
          <CardContent>
            <p className="text-center text-muted-foreground">
              You don't have permission to access company settings.
            </p>
          </CardContent>
        </Card>
      </div>
    );
  }

  if (!currentCompany) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <Card className="w-96">
          <CardHeader>
            <CardTitle className="text-center">No Company Selected</CardTitle>
          </CardHeader>
          <CardContent>
            <p className="text-center text-muted-foreground">
              Please select a company to manage settings.
            </p>
          </CardContent>
        </Card>
      </div>
    );
  }

  const updateCompanyInfo = async (data: CompanyInfoData) => {
    setIsLoading(true);
    try {
      const { error } = await supabase
        .from('companies')
        .update({
          name: data.name,
          email: data.email,
          phone: data.phone,
          address: data.address,
          updated_at: new Date().toISOString(),
        })
        .eq('id', currentCompany.id);

      if (error) throw error;

      toast.success('Company information updated successfully');
      
      // Refresh company data
      if (isSuperAdmin) {
        await refreshCompanies();
      } else {
        await refreshUserCompany();
      }
    } catch (error) {
      console.error('Error updating company info:', error);
      toast.error('Failed to update company information');
    } finally {
      setIsLoading(false);
    }
  };

  const updateApiTokens = async (data: ApiTokensData) => {
    setIsLoading(true);
    try {
      const { error } = await supabase
        .from('companies')
        .update({
          api_tokens: {
            green_api_instance_id: data.green_api_instance_id,
            green_api_token: data.green_api_token,
            whatsapp_number: data.whatsapp_number,
            twilio_account_sid: data.twilio_account_sid,
            twilio_auth_token: data.twilio_auth_token,
            twilio_phone_number: data.twilio_phone_number,
          },
          updated_at: new Date().toISOString(),
        })
        .eq('id', currentCompany.id);

      if (error) throw error;

      toast.success('API tokens updated successfully');
      
      // Refresh company data
      if (isSuperAdmin) {
        await refreshCompanies();
      } else {
        await refreshUserCompany();
      }
    } catch (error) {
      console.error('Error updating API tokens:', error);
      toast.error('Failed to update API tokens');
    } finally {
      setIsLoading(false);
    }
  };

  const copyToClipboard = (text: string, label: string) => {
    navigator.clipboard.writeText(text);
    toast.success(`${label} copied to clipboard`);
  };

  const getWebhookUrl = () => {
    const instanceId = tokensForm.watch('green_api_instance_id');
    if (!instanceId) return '';
    return `https://jihaizhvpddinhdysscd.supabase.co/functions/v1/green-api-webhook?instanceId=${instanceId}`;
  };

  const toggleTokenVisibility = (field: 'green_api_token' | 'twilio_auth_token') => {
    setShowTokens(prev => ({
      ...prev,
      [field]: !prev[field]
    }));
  };

  const getConnectionStatus = (service: 'whatsapp' | 'twilio') => {
    const tokens = currentCompany.api_tokens || {};
    
    if (service === 'whatsapp') {
      const hasTokens = tokens.green_api_instance_id && tokens.green_api_token;
      return hasTokens ? 'connected' : 'disconnected';
    } else {
      const hasTokens = tokens.twilio_account_sid && tokens.twilio_auth_token && tokens.twilio_phone_number;
      return hasTokens ? 'connected' : 'disconnected';
    }
  };

  return (
    <div className="container mx-auto px-4 py-8">
      <div className="mb-8">
        <h1 className="text-3xl font-bold flex items-center gap-3">
          <Settings className="h-8 w-8" />
          Company Settings
        </h1>
        <p className="text-muted-foreground mt-2">
          Manage your company information and API integrations
        </p>
      </div>

      <Tabs defaultValue="company-info" className="space-y-6">
        <TabsList className="grid w-full grid-cols-3">
          <TabsTrigger value="company-info" className="flex items-center gap-2">
            <Building2 className="h-4 w-4" />
            Company Info
          </TabsTrigger>
          <TabsTrigger value="whatsapp" className="flex items-center gap-2">
            <MessageSquare className="h-4 w-4" />
            WhatsApp
          </TabsTrigger>
          <TabsTrigger value="twilio" className="flex items-center gap-2">
            <Phone className="h-4 w-4" />
            Twilio
          </TabsTrigger>
        </TabsList>

        <TabsContent value="company-info" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>Company Information</CardTitle>
            </CardHeader>
            <CardContent>
              <form onSubmit={companyForm.handleSubmit(updateCompanyInfo)} className="space-y-4">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <Label htmlFor="name">Company Name *</Label>
                    <Input
                      id="name"
                      {...companyForm.register('name')}
                      placeholder="Legal Firm Ltd."
                    />
                    {companyForm.formState.errors.name && (
                      <p className="text-sm text-red-600">{companyForm.formState.errors.name.message}</p>
                    )}
                  </div>
                  
                  <div>
                    <Label htmlFor="email">Email *</Label>
                    <Input
                      id="email"
                      type="email"
                      {...companyForm.register('email')}
                      placeholder="<EMAIL>"
                    />
                    {companyForm.formState.errors.email && (
                      <p className="text-sm text-red-600">{companyForm.formState.errors.email.message}</p>
                    )}
                  </div>
                  
                  <div>
                    <Label htmlFor="phone">Phone *</Label>
                    <Input
                      id="phone"
                      {...companyForm.register('phone')}
                      placeholder="+972-50-123-4567"
                    />
                    {companyForm.formState.errors.phone && (
                      <p className="text-sm text-red-600">{companyForm.formState.errors.phone.message}</p>
                    )}
                  </div>
                  
                  <div className="md:col-span-2">
                    <Label htmlFor="address">Address *</Label>
                    <Textarea
                      id="address"
                      {...companyForm.register('address')}
                      placeholder="123 Legal Street, Tel Aviv, Israel"
                      rows={3}
                    />
                    {companyForm.formState.errors.address && (
                      <p className="text-sm text-red-600">{companyForm.formState.errors.address.message}</p>
                    )}
                  </div>
                </div>
                
                <Button type="submit" disabled={isLoading} className="flex items-center gap-2">
                  <Save className="h-4 w-4" />
                  {isLoading ? 'Saving...' : 'Save Changes'}
                </Button>
              </form>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="whatsapp" className="space-y-6">
          <Card>
            <CardHeader>
              <div className="flex items-center justify-between">
                <CardTitle className="flex items-center gap-2">
                  <MessageSquare className="h-5 w-5" />
                  WhatsApp Integration (GreenAPI)
                </CardTitle>
                <Badge
                  variant={getConnectionStatus('whatsapp') === 'connected' ? 'default' : 'secondary'}
                  className="flex items-center gap-1"
                >
                  {getConnectionStatus('whatsapp') === 'connected' ? (
                    <CheckCircle className="h-3 w-3" />
                  ) : (
                    <AlertCircle className="h-3 w-3" />
                  )}
                  {getConnectionStatus('whatsapp') === 'connected' ? 'Connected' : 'Not Connected'}
                </Badge>
              </div>
            </CardHeader>
            <CardContent>
              <form onSubmit={tokensForm.handleSubmit(updateApiTokens)} className="space-y-4">
                <div className="space-y-4">
                  <div>
                    <Label htmlFor="green_api_instance_id">GreenAPI Instance ID</Label>
                    <Input
                      id="green_api_instance_id"
                      {...tokensForm.register('green_api_instance_id')}
                      placeholder="1101000001"
                    />
                  </div>

                  <div>
                    <Label htmlFor="green_api_token">GreenAPI Token</Label>
                    <div className="relative">
                      <Input
                        id="green_api_token"
                        type={showTokens.green_api_token ? 'text' : 'password'}
                        {...tokensForm.register('green_api_token')}
                        placeholder="Enter GreenAPI token"
                        className="pr-10"
                      />
                      <Button
                        type="button"
                        variant="ghost"
                        size="sm"
                        className="absolute right-0 top-0 h-full px-3"
                        onClick={() => toggleTokenVisibility('green_api_token')}
                      >
                        {showTokens.green_api_token ? (
                          <EyeOff className="h-4 w-4" />
                        ) : (
                          <Eye className="h-4 w-4" />
                        )}
                      </Button>
                    </div>
                  </div>

                  <div>
                    <Label htmlFor="whatsapp_number">WhatsApp Number</Label>
                    <Input
                      id="whatsapp_number"
                      {...tokensForm.register('whatsapp_number')}
                      placeholder="972501234567"
                    />
                  </div>

                  {tokensForm.watch('green_api_instance_id') && (
                    <div className="space-y-2">
                      <Label>Webhook URL</Label>
                      <div className="flex items-center gap-2">
                        <Input
                          value={getWebhookUrl()}
                          readOnly
                          className="font-mono text-sm"
                        />
                        <Button
                          type="button"
                          variant="outline"
                          size="sm"
                          onClick={() => copyToClipboard(getWebhookUrl(), 'Webhook URL')}
                        >
                          <Copy className="h-4 w-4" />
                        </Button>
                      </div>
                      <p className="text-sm text-muted-foreground">
                        Configure this URL in your GreenAPI webhook settings
                      </p>
                    </div>
                  )}
                </div>

                <Button type="submit" disabled={isLoading} className="flex items-center gap-2">
                  <Save className="h-4 w-4" />
                  {isLoading ? 'Saving...' : 'Save WhatsApp Settings'}
                </Button>
              </form>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="twilio" className="space-y-6">
          <Card>
            <CardHeader>
              <div className="flex items-center justify-between">
                <CardTitle className="flex items-center gap-2">
                  <Phone className="h-5 w-5" />
                  Twilio Integration
                </CardTitle>
                <Badge
                  variant={getConnectionStatus('twilio') === 'connected' ? 'default' : 'secondary'}
                  className="flex items-center gap-1"
                >
                  {getConnectionStatus('twilio') === 'connected' ? (
                    <CheckCircle className="h-3 w-3" />
                  ) : (
                    <AlertCircle className="h-3 w-3" />
                  )}
                  {getConnectionStatus('twilio') === 'connected' ? 'Connected' : 'Not Connected'}
                </Badge>
              </div>
            </CardHeader>
            <CardContent>
              <form onSubmit={tokensForm.handleSubmit(updateApiTokens)} className="space-y-4">
                <div className="space-y-4">
                  <div>
                    <Label htmlFor="twilio_account_sid">Twilio Account SID</Label>
                    <Input
                      id="twilio_account_sid"
                      {...tokensForm.register('twilio_account_sid')}
                      placeholder="ACxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx"
                    />
                  </div>

                  <div>
                    <Label htmlFor="twilio_auth_token">Twilio Auth Token</Label>
                    <div className="relative">
                      <Input
                        id="twilio_auth_token"
                        type={showTokens.twilio_auth_token ? 'text' : 'password'}
                        {...tokensForm.register('twilio_auth_token')}
                        placeholder="Enter Twilio auth token"
                        className="pr-10"
                      />
                      <Button
                        type="button"
                        variant="ghost"
                        size="sm"
                        className="absolute right-0 top-0 h-full px-3"
                        onClick={() => toggleTokenVisibility('twilio_auth_token')}
                      >
                        {showTokens.twilio_auth_token ? (
                          <EyeOff className="h-4 w-4" />
                        ) : (
                          <Eye className="h-4 w-4" />
                        )}
                      </Button>
                    </div>
                  </div>

                  <div>
                    <Label htmlFor="twilio_phone_number">Twilio Phone Number</Label>
                    <Input
                      id="twilio_phone_number"
                      {...tokensForm.register('twilio_phone_number')}
                      placeholder="+**********"
                    />
                  </div>
                </div>

                <Alert>
                  <AlertCircle className="h-4 w-4" />
                  <AlertDescription>
                    These credentials will be used for making phone calls through Twilio.
                    Make sure your Twilio account has sufficient credits and the phone number is verified.
                  </AlertDescription>
                </Alert>

                <Button type="submit" disabled={isLoading} className="flex items-center gap-2">
                  <Save className="h-4 w-4" />
                  {isLoading ? 'Saving...' : 'Save Twilio Settings'}
                </Button>
              </form>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
};

export default CompanySettingsPage;

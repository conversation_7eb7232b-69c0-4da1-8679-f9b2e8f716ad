import { useState, useEffect, useCallback, useMemo } from 'react';
import { useSearchParams } from 'react-router-dom';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Avatar, AvatarFallback } from '@/components/ui/avatar';
import { Badge } from '@/components/ui/badge';
import { Send, Phone, MoreVertical, User } from 'lucide-react';
import { useWhatsApp, WhatsAppConversation } from '@/hooks/useWhatsApp';
import { useLeads } from '@/hooks/useLeads';
import { Lead } from '@/components/leads/LeadCard';
import { format } from 'date-fns';
import { WhatsAppErrorBoundary } from '@/components/WhatsAppErrorBoundary';

interface WhatsAppPageProps {
  showHeader?: boolean;
  containerHeight?: string;
}

export default function WhatsAppPage({
  showHeader = true,
  containerHeight = "h-[calc(100vh-10rem)]"
}: WhatsAppPageProps = {}) {
  console.log('🔴 NEW WHATSAPP PAGE COMPONENT LOADED 🔴', { showHeader, containerHeight });
  const [searchParams] = useSearchParams();
  const leadId = searchParams.get('lead');
  
  const { 
    conversations, 
    messages, 
    isLoading, 
    isSending,
    fetchMessages, 
    sendMessage, 
    markAsRead 
  } = useWhatsApp();
  
  const { getLeadById } = useLeads();
  
  const [selectedChat, setSelectedChat] = useState<string | null>(null);
  const [newMessage, setNewMessage] = useState('');
  const [leadDetails, setLeadDetails] = useState<Lead | null>(null);
  const [isLeadLoading, setIsLeadLoading] = useState(false);
  const [conversationLeads, setConversationLeads] = useState<Record<string, Lead>>({});

  const selectedConversation = useMemo(() => 
    conversations.find(conv => conv.id === selectedChat), 
    [conversations, selectedChat]
  );

  // Fetch lead details when leadId is provided
  useEffect(() => {
    const fetchLeadDetails = async () => {
      if (leadId && !leadDetails) {
        setIsLeadLoading(true);
        try {
          const lead = await getLeadById(leadId);
          setLeadDetails(lead);
        } catch (error) {
          console.error('Error fetching lead details:', error);
        } finally {
          setIsLeadLoading(false);
        }
      }
    };

    fetchLeadDetails();
  }, [leadId, getLeadById, leadDetails]);

  // Fetch lead details for conversations
  useEffect(() => {
    const fetchConversationLeads = async () => {
      const leadsToFetch = conversations.filter(conv => conv.lead_id && !conversationLeads[conv.lead_id]);
      
      for (const conv of leadsToFetch) {
        if (conv.lead_id) {
          try {
            const lead = await getLeadById(conv.lead_id);
            if (lead) {
              setConversationLeads(prev => ({ ...prev, [conv.lead_id!]: lead }));
            }
          } catch (error) {
            console.error('Error fetching lead details for conversation:', error);
          }
        }
      }
    };

    if (conversations.length > 0) {
      fetchConversationLeads();
    }
  }, [conversations, conversationLeads, getLeadById]);

  // Handle lead conversation selection
  useEffect(() => {
    if (leadId && conversations.length > 0) {
      // Find conversation for the specific lead
      const leadConversation = conversations.find(conv => conv.lead_id === leadId);
      if (leadConversation && selectedChat !== leadConversation.id) {
        setSelectedChat(leadConversation.id);
      }
    }
  }, [leadId, conversations, selectedChat]);

  // Handle messages fetch when chat is selected
  useEffect(() => {
    if (selectedChat) {
      fetchMessages(selectedChat);
      markAsRead(selectedChat);
    }
  }, [selectedChat, fetchMessages, markAsRead]);

  const handleSendMessage = useCallback(async () => {
    if (!newMessage.trim() || isSending) return;
    
    try {
      if (selectedConversation) {
        // Existing conversation
        await sendMessage(
          selectedConversation.phone_number,
          newMessage.trim(),
          selectedConversation.lead_id || undefined
        );
      } else if (leadDetails) {
        // New conversation with lead
        await sendMessage(
          leadDetails.phone,
          newMessage.trim(),
          leadDetails.id
        );
      }
      setNewMessage('');
    } catch (error) {
      console.error('Failed to send message:', error);
    }
  }, [newMessage, isSending, selectedConversation, leadDetails, sendMessage]);

  const handleSelectConversation = useCallback((conversation: WhatsAppConversation) => {
    if (selectedChat !== conversation.id) {
      setSelectedChat(conversation.id);
    }
  }, [selectedChat]);

  const formatTime = (timestamp: string | null) => {
    if (!timestamp) return '';
    return format(new Date(timestamp), 'HH:mm');
  };

  const formatDate = (dateString: string) => {
    return format(new Date(dateString), 'MMM dd, yyyy HH:mm');
  };

  if (isLoading) {
    return (
      <div className="p-6">
        <div className="mb-6">
          <h1 className="text-3xl font-bold">וואטסאפ</h1>
          <p className="text-muted-foreground mt-2">טוען שיחות...</p>
        </div>
      </div>
    );
  }

  return (
    <WhatsAppErrorBoundary>
      <div className={`${containerHeight} flex flex-col bg-background`}>
        {/* Conditional Header */}
        {showHeader && (
          <div className="flex-shrink-0 p-6 border-b">
            <h1 className="text-3xl font-bold">וואטסאפ</h1>
            <p className="text-muted-foreground mt-2">
              נהל את השיחות וההודעות שלך בוואטסאפ
            </p>
          </div>
        )}

        {/* Main Content Area - Fixed Height */}
        <div className="flex-1 flex overflow-hidden">
          {/* Chats Container */}
          <div className="w-1/3 border-r border-border shadow-[0_0_15px_rgba(59,130,246,0.4)] border-l-2 border-l-blue-400">
            <div className="h-full flex flex-col">
              <div className="p-4 border-b border-border">
                <h2 className="text-lg font-semibold">שיחות</h2>
              </div>
              <div className="flex-1 overflow-y-auto">
                <div className="p-2 space-y-1">
                  {conversations.length === 0 ? (
                    <div className="p-4 text-center text-muted-foreground">
                      אין שיחות עדיין
                    </div>
                  ) : (
                    conversations.map((conversation) => {
                      const conversationLead = conversation.lead_id ? conversationLeads[conversation.lead_id] : null;
                      return (
                        <div
                          key={conversation.id}
                          className={`p-3 rounded-lg cursor-pointer hover:bg-accent/50 transition-colors ${
                            selectedChat === conversation.id
                              ? 'bg-primary/10 border border-primary/20'
                              : 'hover:bg-muted/50'
                          }`}
                          onClick={() => handleSelectConversation(conversation)}
                        >
                          <div className="flex items-center gap-3">
                            <Avatar className="h-10 w-10">
                              <AvatarFallback>
                                {conversation.contact_name?.split(' ').map(n => n[0]).join('') || 'U'}
                              </AvatarFallback>
                            </Avatar>
                            <div className="flex-1 min-w-0">
                              <div className="flex items-center justify-between">
                                <h4 className="font-medium truncate">
                                  {conversation.contact_name || conversation.phone_number}
                                </h4>
                                <span className="text-xs text-muted-foreground">
                                  {conversation.last_message_timestamp ?
                                    formatTime(conversation.last_message_timestamp) : ''}
                                </span>
                              </div>
                              <p className="text-sm text-muted-foreground truncate">
                                {conversation.last_message || 'אין הודעות עדיין'}
                              </p>
                              {conversationLead && (
                                <div className="flex items-center gap-2 mt-1">
                                  <Badge variant="outline" className="text-xs">
                                    {conversationLead.status}
                                  </Badge>
                                  {conversationLead.value && (
                                    <Badge variant="secondary" className="text-xs">
                                      ₪{conversationLead.value.toLocaleString()}
                                    </Badge>
                                  )}
                                </div>
                              )}
                            </div>
                            {conversation.unread_count > 0 && (
                              <Badge variant="default" className="ml-2">
                                {conversation.unread_count}
                              </Badge>
                            )}
                          </div>
                        </div>
                      );
                    })
                  )}
                </div>
              </div>
            </div>
          </div>

          {/* Conversations Container */}
          <div className="flex-1 shadow-[0_0_15px_rgba(59,130,246,0.4)] border-r-2 border-r-blue-400 border-l">
            <div className="h-full flex flex-col">
              {selectedConversation ? (
                <>
                  {/* Chat Header */}
                  <div className="flex items-center justify-between p-4 border-b border-border bg-muted/30">
                    <div className="flex items-center gap-3">
                      <Avatar className="h-10 w-10">
                        <AvatarFallback>
                          {selectedConversation.contact_name?.split(' ').map(n => n[0]).join('') || 'U'}
                        </AvatarFallback>
                      </Avatar>
                      <div>
                        <h3 className="font-semibold">
                          {selectedConversation.contact_name || selectedConversation.phone_number}
                        </h3>
                        <p className="text-sm text-muted-foreground">
                          {selectedConversation.phone_number}
                        </p>
                        {selectedConversation.lead_id && conversationLeads[selectedConversation.lead_id] && (
                          <div className="flex items-center gap-2 mt-1">
                            <Badge variant="outline" className="text-xs">
                              {conversationLeads[selectedConversation.lead_id].status}
                            </Badge>
                            {conversationLeads[selectedConversation.lead_id].value && (
                              <Badge variant="secondary" className="text-xs">
                                ₪{conversationLeads[selectedConversation.lead_id].value.toLocaleString()}
                              </Badge>
                            )}
                          </div>
                        )}
                      </div>
                    </div>
                    <div className="flex items-center gap-2">
                      <Button variant="ghost" size="icon">
                        <Phone className="h-4 w-4" />
                      </Button>
                      <Button variant="ghost" size="icon">
                        <MoreVertical className="h-4 w-4" />
                      </Button>
                    </div>
                  </div>

                  {/* Messages Area */}
                  <div className="flex-1 overflow-y-auto bg-background/50">
                    <div className="p-4 space-y-4">
                      {messages.length === 0 ? (
                        <div className="text-center text-muted-foreground py-8">
                          אין הודעות בשיחה זו
                        </div>
                      ) : (
                        messages.map((message) => (
                          <div
                            key={message.id}
                            className={`flex ${
                              message.sender_type === 'outgoing' ? 'justify-end' : 'justify-start'
                            }`}
                          >
                            <div
                              className={`max-w-[70%] rounded-lg p-3 ${
                                message.sender_type === 'outgoing'
                                  ? 'bg-primary text-primary-foreground'
                                  : 'bg-success/20 text-foreground'
                              }`}
                            >
                              <p className="text-sm">{message.content}</p>
                              <p className="text-xs opacity-70 mt-1">
                                {formatDate(message.created_at)}
                              </p>
                            </div>
                          </div>
                        ))
                      )}
                    </div>
                  </div>

                  {/* Message Input */}
                  <div className="p-4 border-t bg-background/80">
                    <div className="flex gap-2">
                      <Input
                        placeholder="הקלד הודעה..."
                        value={newMessage}
                        onChange={(e) => setNewMessage(e.target.value)}
                        onKeyDown={(e) => e.key === 'Enter' && handleSendMessage()}
                        disabled={isSending}
                      />
                      <Button
                        onClick={handleSendMessage}
                        disabled={!newMessage.trim() || isSending}
                        size="icon"
                      >
                        <Send className="h-4 w-4" />
                      </Button>
                    </div>
                  </div>
                </>
              ) : leadDetails && !isLeadLoading ? (
                <>
                  {/* New Lead Chat Header */}
                  <div className="flex items-center justify-between p-4 border-b border-border bg-muted/30">
                    <div className="flex items-center gap-3">
                      <Avatar className="h-10 w-10">
                        <AvatarFallback>
                          <User className="h-4 w-4" />
                        </AvatarFallback>
                      </Avatar>
                      <div>
                        <h3 className="font-semibold">{leadDetails.full_name}</h3>
                        <p className="text-sm text-muted-foreground">
                          {leadDetails.phone}
                        </p>
                        <div className="flex items-center gap-2 mt-1">
                          <Badge variant="outline" className="text-xs">
                            {leadDetails.status}
                          </Badge>
                          {leadDetails.value && (
                            <Badge variant="secondary" className="text-xs">
                              ₪{leadDetails.value.toLocaleString()}
                            </Badge>
                          )}
                        </div>
                      </div>
                    </div>
                    <div className="flex items-center gap-2">
                      <Button variant="ghost" size="icon">
                        <Phone className="h-4 w-4" />
                      </Button>
                      <Button variant="ghost" size="icon">
                        <MoreVertical className="h-4 w-4" />
                      </Button>
                    </div>
                  </div>

                  {/* New Conversation Message Area */}
                  <div className="flex-1 flex items-center justify-center bg-background/50">
                    <div className="text-center p-8">
                      <div className="bg-muted rounded-full p-4 mb-4 mx-auto w-fit">
                        <User className="h-8 w-8 text-muted-foreground" />
                      </div>
                      <h3 className="text-lg font-medium mb-2">התחל שיחה</h3>
                      <p className="text-muted-foreground mb-4 max-w-sm">
                        שלח את ההודעה הראשונה ל{leadDetails.full_name} כדי להתחיל שיחת וואטסאפ.
                      </p>
                      <div className="bg-accent/50 rounded-lg p-3 text-sm text-muted-foreground">
                        <strong>פרטי ליד:</strong><br />
                        סטטוס: {leadDetails.status}<br />
                        {leadDetails.source && `מקור: ${leadDetails.source}`}<br />
                        {leadDetails.value && `ערך: ₪${leadDetails.value.toLocaleString()}`}
                      </div>
                    </div>
                  </div>

                  {/* Message Input for New Lead */}
                  <div className="p-4 border-t bg-background/80">
                    <div className="flex gap-2">
                      <Input
                        placeholder={`שלח הודעה ראשונה ל${leadDetails.full_name}...`}
                        value={newMessage}
                        onChange={(e) => setNewMessage(e.target.value)}
                        onKeyDown={(e) => e.key === 'Enter' && handleSendMessage()}
                        disabled={isSending}
                      />
                      <Button
                        onClick={handleSendMessage}
                        disabled={!newMessage.trim() || isSending}
                        size="icon"
                      >
                        <Send className="h-4 w-4" />
                      </Button>
                    </div>
                  </div>
                </>
              ) : (
                <div className="flex-1 flex items-center justify-center bg-background/50">
                  <div className="text-center text-muted-foreground">
                    {isLeadLoading ? (
                      <>
                        <h3 className="text-lg font-medium mb-2">טוען פרטי ליד...</h3>
                        <p>אנא המתן בזמן שאנו מביאים את פרטי הליד</p>
                      </>
                    ) : (
                      <>
                        <h3 className="text-lg font-medium mb-2">בחר שיחה</h3>
                        <p>בחר שיחה משמאל כדי להתחיל להודיע</p>
                      </>
                    )}
                  </div>
                </div>
              )}
            </div>
          </div>
        </div>
      </div>
    </WhatsAppErrorBoundary>
  );
}
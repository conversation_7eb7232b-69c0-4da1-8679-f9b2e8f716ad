import React, { useState, useMemo } from "react";
import { useNavigate } from "react-router-dom";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Plus, Eye, Calendar, User, FileText, Loader2 } from "lucide-react";
import { useCases } from "@/hooks/useCases";
import { CaseModal } from "@/components/case/CaseModal";
import { ErrorBoundary } from "@/components/ErrorBoundary";

const CaseIntakePage = () => {
  const navigate = useNavigate();
  const [isModalOpen, setIsModalOpen] = useState(false);
  const { cases, caseTypes, isLoading } = useCases();

  const intakeCases = useMemo(() => 
    cases.filter(case_ => case_.status === "בקליטה"), 
    [cases]
  );

  const CasesTable = ({ cases }: { cases: typeof intakeCases }) => (
    <div className="card-professional rounded-lg overflow-hidden">
      <table className="professional-table">
        <thead>
          <tr>
            <th>פעולות</th>
            <th>דדליין</th>
            <th>סוג התיק</th>
            <th>לקוח</th>
            <th>שם התיק</th>
          </tr>
        </thead>
        <tbody>
          {cases.map((case_) => (
            <tr
              key={case_.id}
              className="cursor-pointer hover:bg-muted/50"
              onClick={() => navigate(`/case/${case_.id}`)}
            >
              <td>
                <Button
                  size="sm"
                  variant="ghost"
                  className="h-8 w-8 p-0"
                  onClick={(e) => {
                    e.stopPropagation();
                    navigate(`/case/${case_.id}`);
                  }}
                >
                  <Eye className="w-4 h-4" />
                </Button>
              </td>
              <td className="text-sm">
                <div className="flex items-center gap-2">
                  <Calendar className="w-4 h-4 text-muted-foreground" />
                  {case_.deadline ? new Date(case_.deadline).toLocaleDateString('he-IL') : '-'}
                </div>
              </td>
              <td>
                <span className="text-sm font-medium">{case_.case_type?.name || '-'}</span>
              </td>
              <td>
                <div className="flex items-center gap-2">
                  <User className="w-4 h-4 text-muted-foreground" />
                  {case_.lead?.full_name || '-'}
                </div>
              </td>
              <td className="font-medium">
                <div className="flex items-center gap-2">
                  <FileText className="w-4 h-4 text-muted-foreground" />
                  {case_.title}
                </div>
              </td>
            </tr>
          ))}
        </tbody>
      </table>
    </div>
  );

  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="flex items-center gap-3">
          <Loader2 className="h-5 w-5 animate-spin" />
          <span>טוען תיקים...</span>
        </div>
      </div>
    );
  }

  return (
    <ErrorBoundary>
      <div className="space-y-6">
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold text-foreground mb-2">תיקים בקליטה</h1>
            <p className="text-muted-foreground">תיקים חדשים הממתינים לעיבוד</p>
          </div>
          <Button 
            className="btn-professional flex items-center gap-2"
            onClick={() => setIsModalOpen(true)}
          >
            <Plus className="w-4 h-4" />
            צור תיק חדש
          </Button>
        </div>

        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <div className="w-3 h-3 bg-warning rounded-full"></div>
              תיקים בקליטה ({intakeCases.length})
            </CardTitle>
          </CardHeader>
          <CardContent>
            {intakeCases.length > 0 ? (
              <CasesTable cases={intakeCases} />
            ) : (
              <div className="text-center py-8 text-muted-foreground">
                אין תיקים בקליטה כרגע
              </div>
            )}
          </CardContent>
        </Card>

        <CaseModal
          isOpen={isModalOpen}
          onClose={() => setIsModalOpen(false)}
          caseTypes={caseTypes}
          onSuccess={() => setIsModalOpen(false)}
        />
      </div>
    </ErrorBoundary>
  );
};

export default CaseIntakePage;
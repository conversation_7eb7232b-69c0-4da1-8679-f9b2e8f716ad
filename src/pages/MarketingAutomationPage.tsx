import { useState } from 'react';
import { <PERSON>, <PERSON><PERSON>ontent, Card<PERSON><PERSON>er, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { 
  Plus, 
  Workflow, 
  Play, 
  Pause, 
  Edit, 
  Trash2, 
  Eye,
  Activity,
  Clock,
  CheckCircle,
  AlertTriangle
} from 'lucide-react';
import { WorkflowBuilder } from '@/components/marketing/WorkflowBuilder';
import { WorkflowList } from '@/components/marketing/WorkflowList';
import { WorkflowExecutions } from '@/components/marketing/WorkflowExecutions';
import { useWorkflows } from '@/hooks/useWorkflows';

const MarketingAutomationPage = () => {
  const [activeTab, setActiveTab] = useState('workflows');
  const [isBuilderOpen, setIsBuilderOpen] = useState(false);
  const [editingWorkflow, setEditingWorkflow] = useState(null);
  
  const { 
    workflows, 
    isLoading, 
    createWorkflow, 
    updateWorkflow, 
    deleteWorkflow, 
    toggleWorkflow 
  } = useWorkflows();

  const handleCreateWorkflow = () => {
    setEditingWorkflow(null);
    setIsBuilderOpen(true);
  };

  const handleEditWorkflow = (workflow: any) => {
    setEditingWorkflow(workflow);
    setIsBuilderOpen(true);
  };

  const handleSaveWorkflow = async (workflowData: any) => {
    try {
      if (editingWorkflow) {
        await updateWorkflow(editingWorkflow.id, workflowData);
      } else {
        await createWorkflow(workflowData);
      }
      setIsBuilderOpen(false);
      setEditingWorkflow(null);
    } catch (error) {
      console.error('Error saving workflow:', error);
    }
  };

  const handleDeleteWorkflow = async (workflowId: string) => {
    if (confirm('האם אתה בטוח שברצונך למחוק את הזרימה? פעולה זו לא ניתנת לביטול.')) {
      await deleteWorkflow(workflowId);
    }
  };

  const handleToggleWorkflow = async (workflowId: string) => {
    await toggleWorkflow(workflowId);
  };

  // Calculate stats
  const activeWorkflows = workflows.filter(w => w.is_active).length;
  const totalWorkflows = workflows.length;

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-foreground mb-2 flex items-center gap-3">
            <Workflow className="h-8 w-8 text-primary" />
            אוטומציה שיווקית
          </h1>
          <p className="text-muted-foreground">
            צור זרימות עבודה אוטומטיות לניהול לידים ותיקים
          </p>
        </div>
        <Button 
          onClick={handleCreateWorkflow}
          className="flex items-center gap-2"
        >
          <Plus className="h-4 w-4" />
          צור זרימה חדשה
        </Button>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-muted-foreground">סה"כ זרימות</p>
                <p className="text-2xl font-bold">{totalWorkflows}</p>
              </div>
              <Workflow className="h-8 w-8 text-muted-foreground" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-muted-foreground">זרימות פעילות</p>
                <p className="text-2xl font-bold text-green-600">{activeWorkflows}</p>
              </div>
              <Play className="h-8 w-8 text-green-600" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-muted-foreground">זרימות מושבתות</p>
                <p className="text-2xl font-bold text-orange-600">{totalWorkflows - activeWorkflows}</p>
              </div>
              <Pause className="h-8 w-8 text-orange-600" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-muted-foreground">ביצועים השבוע</p>
                <p className="text-2xl font-bold text-blue-600">-</p>
              </div>
              <Activity className="h-8 w-8 text-blue-600" />
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Main Content */}
      <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full" dir="rtl">
        <TabsList className="grid w-full grid-cols-2 bg-muted/30">
          <TabsTrigger value="workflows" className="text-sm font-medium">
            זרימות עבודה
          </TabsTrigger>
          <TabsTrigger value="executions" className="text-sm font-medium">
            יומן ביצועים
          </TabsTrigger>
        </TabsList>

        <TabsContent value="workflows" className="mt-6">
          <WorkflowList
            workflows={workflows}
            isLoading={isLoading}
            onEdit={handleEditWorkflow}
            onDelete={handleDeleteWorkflow}
            onToggle={handleToggleWorkflow}
          />
        </TabsContent>

        <TabsContent value="executions" className="mt-6">
          <WorkflowExecutions />
        </TabsContent>
      </Tabs>

      {/* Workflow Builder Modal */}
      {isBuilderOpen && (
        <WorkflowBuilder
          isOpen={isBuilderOpen}
          onClose={() => {
            setIsBuilderOpen(false);
            setEditingWorkflow(null);
          }}
          onSave={handleSaveWorkflow}
          editingWorkflow={editingWorkflow}
        />
      )}
    </div>
  );
};

export default MarketingAutomationPage;

import { <PERSON>, PhoneOff, Mic, MicO<PERSON>, Clock } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Card } from '@/components/ui/card';
import { ActiveCall } from '@/hooks/useCallManager';
import { useEffect, useState } from 'react';

interface ActiveCallBarProps {
  activeCall: ActiveCall;
  onHangup: () => void;
  onMute: () => void;
  onUnmute: () => void;
}

export const ActiveCallBar = ({ activeCall, onHangup, onMute, onUnmute }: ActiveCallBarProps) => {
  const [duration, setDuration] = useState(0);

  useEffect(() => {
    const interval = setInterval(() => {
      const now = new Date();
      const elapsed = Math.floor((now.getTime() - activeCall.startTime.getTime()) / 1000);
      setDuration(elapsed);
    }, 1000);

    return () => clearInterval(interval);
  }, [activeCall.startTime]);

  const formatDuration = (seconds: number) => {
    const mins = Math.floor(seconds / 60);
    const secs = seconds % 60;
    return `${mins.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
  };

  const getStatusColor = () => {
    switch (activeCall.status) {
      case 'connecting':
        return 'text-warning';
      case 'connected':
        return 'text-success';
      case 'muted':
        return 'text-muted-foreground';
      default:
        return 'text-foreground';
    }
  };

  return (
    <Card className="fixed top-4 left-1/2 transform -translate-x-1/2 z-50 bg-background border shadow-lg">
      <div className="flex items-center gap-4 p-4">
        <div className="flex items-center gap-2">
          <Phone className={`w-5 h-5 ${getStatusColor()}`} />
          <div>
            <p className="font-semibold text-foreground">{activeCall.leadName}</p>
            <p className="text-sm text-muted-foreground">{activeCall.phoneNumber}</p>
          </div>
        </div>

        <div className="flex items-center gap-2 text-sm">
          <Clock className="w-4 h-4 text-muted-foreground" />
          <span className="font-mono text-foreground">{formatDuration(duration)}</span>
        </div>

        <div className={`px-2 py-1 rounded-full text-xs font-medium ${
          activeCall.status === 'connecting' ? 'bg-warning/20 text-warning' :
          activeCall.status === 'connected' ? 'bg-success/20 text-success' :
          activeCall.status === 'muted' ? 'bg-muted text-muted-foreground' :
          'bg-muted text-muted-foreground'
        }`}>
          {activeCall.status === 'connecting' && 'מתחבר...'}
          {activeCall.status === 'connected' && 'מחובר'}
          {activeCall.status === 'muted' && 'מושתק'}
        </div>

        <div className="flex items-center gap-2">
          {activeCall.status === 'muted' ? (
            <Button
              size="sm"
              variant="outline"
              onClick={onUnmute}
              className="flex items-center gap-1"
            >
              <Mic className="w-4 h-4" />
              בטל השתקה
            </Button>
          ) : (
            <Button
              size="sm"
              variant="outline"
              onClick={onMute}
              className="flex items-center gap-1"
            >
              <MicOff className="w-4 h-4" />
              השתק
            </Button>
          )}

          <Button
            size="sm"
            variant="destructive"
            onClick={onHangup}
            className="flex items-center gap-1"
          >
            <PhoneOff className="w-4 h-4" />
            נתק
          </Button>
        </div>
      </div>
    </Card>
  );
};
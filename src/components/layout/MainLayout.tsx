import { Outlet } from "react-router-dom";
import { SidebarTrigger } from "@/components/ui/sidebar";
import { AppSidebar } from "@/components/layout/AppSidebar";
import { Header } from "@/components/layout/Header";

const MainLayout = () => {
  return (
    <>
      <AppSidebar />
      <div className="flex flex-col min-h-screen w-full">
        <Header />
        <main className="flex-1 p-6 bg-background">
          <Outlet />
        </main>
      </div>
    </>
  );
};

export default MainLayout;